import React from 'react';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '@tailwind';

const fullConfig = resolveConfig(tailwindConfig);
const { text_primary, neutral_300 } = fullConfig.theme.colors;

/**
 * @param {number} progress - A number between 0 and 1 (e.g., 0.33 for 33%)
 */
const ProgressCircle = ({ CIRCLE_SIZE = 20, STROKE_WIDTH = 1.3, progress }) => {
  const RADIUS = (CIRCLE_SIZE - STROKE_WIDTH) / 2;
  const CIRCUMFERENCE = 2 * Math.PI * RADIUS;

  // Clamp progress between 0 and 1
  const pct = Math.max(0, Math.min(1, progress));
  const offset = CIRCUMFERENCE * (1 - pct);

  return (
    <svg width={CIRCLE_SIZE} height={CIRCLE_SIZE} viewBox={`0 0 ${CIRCLE_SIZE} ${CIRCLE_SIZE}`}>
      {/* Background circle */}
      <circle
        cx={CIRCLE_SIZE / 2}
        cy={CIRCLE_SIZE / 2}
        r={RADIUS}
        stroke={neutral_300}
        strokeWidth={STROKE_WIDTH}
        fill="none"
      />
      {/* Progress circle */}
      <circle
        cx={CIRCLE_SIZE / 2}
        cy={CIRCLE_SIZE / 2}
        r={RADIUS}
        stroke={text_primary}
        strokeWidth={STROKE_WIDTH}
        fill="none"
        strokeDasharray={CIRCUMFERENCE}
        strokeDashoffset={offset}
        strokeLinecap="round"
        style={{ transition: 'stroke-dashoffset 0.3s' }}
        transform={`rotate(-90 ${CIRCLE_SIZE / 2} ${CIRCLE_SIZE / 2})`}
      />
    </svg>
  );
};

export default ProgressCircle;
