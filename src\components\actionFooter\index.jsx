import React from 'react';
import PrimaryButton from '@/components/shared/primaryButton';
import SecondaryButton from '@/components/shared/secondaryButton';
import tw from 'twin.macro';

/**
 * A customizable footer component that displays primary and secondary action buttons.
 *
 * @param {Object} props
 * @param {Object} [props.containerStyle] - Tailwind styles for the footer container
 * @param {Object} [props.primaryButtonProps] - Props for the PrimaryButton (text, type, customStyle, onClick, etc.)
 * @param {Object} [props.secondaryButtonProps] - Props for the SecondaryButton (text, type, customStyle, onClick, etc.)
 * @param {boolean} props.isPrimaryFirst - If true, renders the primary button first; otherwise, renders the secondary button first
 *
 * @returns {JSX.Element} Rendered action footer component
 */

const ActionFooter = ({
  containerStyle,
  secondaryButtonProps,
  primaryButtonProps,
  isPrimaryFirst,
}) => {
  const buttons = [
    Object.keys(primaryButtonProps || {}).length > 0 && (
      <PrimaryButton key="primary" {...primaryButtonProps} />
    ),
    Object.keys(secondaryButtonProps || {}).length > 0 && (
      <SecondaryButton key="secondary" {...secondaryButtonProps} />
    ),
  ].filter(Boolean);

  const orderedButtons = isPrimaryFirst ? buttons : buttons.reverse();

  return (
    <div css={[tw`flex items-center justify-center`, containerStyle]}>
      {orderedButtons.map((btn, i) => React.cloneElement(btn, { key: i }))}
    </div>
  );
};

export default ActionFooter;
