import * as yup from 'yup';

export const textToSpeechSchema = yup.object().shape({
  text: yup
    .string()
    .max(1000, 'Invalid text.')
    .required('Required field.')
    .test('not-empty-or-whitespace', 'Invalid text.', value => !!value && value.trim().length > 0),
  cloningVoice: yup
    .object({
      value: yup.string().required('Required field.'),
      label: yup.string().required('Required field.'),
    })
    .required('Required field.'),
});
