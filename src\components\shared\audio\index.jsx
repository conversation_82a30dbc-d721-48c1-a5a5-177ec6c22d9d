import React, { useState, useRef, useEffect } from 'react';
import tw from 'twin.macro';
import PlayIcon from '@assets/svgs/audio/play.svg';
import PauseIcon from '@assets/svgs/audio/pause.svg';
import DeleteIcon from '@assets/svgs/audio/delete.svg';
import { formatTime } from '@/utils/helpers';

/**
 * Audio component
 *
 * @param {Object} props
 * @param {string} props.audioUrl - The URL of the audio file to play.
 * @param {number} [props.duration=30] - The duration of the audio in seconds.
 * @param {boolean} [props.showElapsedTime] - Whether to display the elapsed time and total duration.
 * @param {Function} props.handleDeleteAudio - Callback function to handle the deletion of the audio.
 * @param {string} [props.currentlyPlayingAudioUrl] - The URL of the currently playing audio (optional, used for managing concurrent playback).
 * @param {Function} [props.setCurrentlyPlayingAudioUrl] - Callback function to set the currently playing audio URL (optional, used for managing concurrent playback).
 *
 * @returns {React.ReactElement} The rendered Audio component.
 */

const Audio = ({
  audioUrl = 'https://p.scdn.co/mp3-preview/2726a9595503bf33fdf44d0e85ae8abc7d876d44?cid=774b29d4f13844c495f206cafdad9c86',
  duration = 30,
  showElapsedTime, // Whether to show elapsed time
  handleDeleteAudio,
  currentlyPlayingAudioUrl,
  setCurrentlyPlayingAudioUrl,
  containerStyle,
  playPauseStyle,
  actionContainerStyle,
  shouldPause,
  customActionbtns,
  hideActions,
  disableButtons,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0); // State to track audio progress
  const [elapsedTime, setElapsedTime] = useState(0);

  const audioRef = useRef(null);
  const progressBarRef = useRef(null); // Ref for the progress bar
  const animationFrameRef = useRef(null); // Ref for requestAnimationFrame
  const isDraggingRef = useRef(false); // Ref to track dragging state
  const playIntervalRef = useRef(null);

  useEffect(() => {
    // Check if the browser supports the MediaSession API
    if ('mediaSession' in navigator) {
      // Set a handler for the 'play' action triggered by media keys or external controls
      navigator.mediaSession.setActionHandler('play', () => {
        playAudio(); // Call the playAudio function to start audio playback
      });

      // Set a handler for the 'pause' action triggered by media keys or external controls
      navigator.mediaSession.setActionHandler('pause', () => {
        pauseAudio(); // Call the pauseAudio function to pause audio playback
      });
    }
  }, []);

  useEffect(() => {
    const updateProgress = () => {
      if (audioRef.current) {
        const currentTime = audioRef.current.currentTime;

        //console.log('duration', duration);
        setProgress((currentTime / (duration || 1)) * 100); // Calculate progress as a percentage
      }

      if (isPlaying) {
        animationFrameRef.current = requestAnimationFrame(updateProgress); // Schedule the next frame
      }
    };

    if (isPlaying) {
      animationFrameRef.current = requestAnimationFrame(updateProgress); // Start the animation loop
      return () => cancelAnimationFrame(animationFrameRef.current); // Cleanup on unmount or pause
    }
  }, [isPlaying]);

  useEffect(() => {
    // Pause the audio if the currentlyPlayingAudioUrl doesn't match this audio's URL
    if (currentlyPlayingAudioUrl && currentlyPlayingAudioUrl !== audioUrl && isPlaying) {
      pauseAudio();
    }
  }, [currentlyPlayingAudioUrl]);

  useEffect(() => {
    if (shouldPause) {
      pauseAudio(); // Pause the audio if shouldPause is true
    }
  }, [shouldPause]);
  const playAudio = () => {
    if (audioRef.current) {
      if (setCurrentlyPlayingAudioUrl) {
        // Set this audio as the currently playing audio
        setCurrentlyPlayingAudioUrl(audioUrl);
      }

      // Play the audio
      audioRef.current.play();
      setIsPlaying(true);

      // Start updating elapsed time
      playIntervalRef.current = setInterval(() => {
        setElapsedTime(prevTime => prevTime + 1);
      }, 1000); // Update elapsed time every second
    }
  };

  const pauseAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
      // Stop updating elapsed time
      clearInterval(playIntervalRef.current);
    }
  };

  const stopAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause(); // Pause the audio
      audioRef.current.currentTime = 0; // Reset playback position to the beginning
      clearInterval(playIntervalRef.current); // Stop the interval that updates elapsed time
      setIsPlaying(false); // Update state to indicate audio is not playing
      setElapsedTime(0); // Reset elapsed time to 00:00
      setProgress(0); // Reset progress bar to 0%
    }
  };

  {
    /* PROGRESS BAR LOGIC*/
  }
  const handleProgressBarClick = e => {
    if (audioRef.current && progressBarRef.current) {
      // Get the bounding rectangle of the progress bar
      const rect = progressBarRef.current.getBoundingClientRect();
      // Calculate the click position as a percentage of the progress bar width
      const clickPosition = Math.min(Math.max((e.clientX - rect.left) / rect.width, 0), 1); // Ensure position is between 0 and 1
      // Update the audio's current time based on the click position
      audioRef.current.currentTime = clickPosition * duration;
      //console.log('new current time', audioRef.current.currentTime);
      // Update the progress state immediately
      setProgress(clickPosition * 100);
      // Update elapsed time immediately
      setElapsedTime(audioRef.current.currentTime);
    }
  };

  const handleThumbDrag = e => {
    if (progressBarRef.current && audioRef.current) {
      const rect = progressBarRef.current.getBoundingClientRect();
      const dragPosition = Math.min(Math.max((e.clientX - rect.left) / rect.width, 0), 1); // Ensure position is between 0 and 1

      const newTime = dragPosition * duration; // Calculate new time based on drag position
      setProgress(dragPosition * 100); // Update progress state
      setElapsedTime(newTime); // Update elapsed time state
      audioRef.current.currentTime = newTime; // Update audio playback position
    }
  };

  const handleMouseDown = e => {
    e.preventDefault(); // Prevent default browser behavior
    if (audioRef.current) {
      isDraggingRef.current = true; // Set dragging state to true

      // If playback is currently playing, pause the audio and clear the interval
      if (isPlaying) {
        audioRef.current.pause(); // Pause the audio
        clearInterval(playIntervalRef.current); // Clear the interval that updates elapsed time
      }

      // Attach mousemove and mouseup listeners to the document
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
  };

  const handleMouseUp = () => {
    isDraggingRef.current = false; // Set dragging state to false
    if (audioRef.current) {
      // If playback was playing before dragging, resume playback and set the interval
      if (isPlaying) {
        audioRef.current.play(); // Resume audio playback
        playIntervalRef.current = setInterval(() => {
          setElapsedTime(prevTime => prevTime + 1); // Update elapsed time every second
        }, 1000);
      }
    }

    // Remove mousemove and mouseup listeners from the document
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = e => {
    if (isDraggingRef.current) {
      handleThumbDrag(e);
    }
  };
  return (
    <div css={[tw`w-full h-fit flex items-center gap-[6%]`, containerStyle]}>
      {/* Play/Pause button */}
      <div css={[tw`w-[15%] flex-shrink-0`, playPauseStyle]}>
        <img
          onClick={disableButtons ? null : isPlaying ? pauseAudio : playAudio}
          src={isPlaying ? PauseIcon : PlayIcon}
          alt={isPlaying ? 'pause audio' : 'resume audio'}
          css={[tw`w-full h-full cursor-pointer`, disableButtons && tw`cursor-not-allowed`]}
        />
      </div>
      {/* progress bar section*/}
      <div css={tw`flex-grow relative`}>
        <div css={tw`w-full`}>
          <audio ref={audioRef} src={audioUrl} onEnded={stopAudio} />
          <div tw="w-full bg-neutral_300 rounded-full">
            <div
              ref={progressBarRef}
              css={[
                tw`w-full h-[10px] rounded  cursor-pointer relative`,
                disableButtons && tw`cursor-not-allowed`,
              ]}
              onClick={disableButtons ? null : handleProgressBarClick}
            >
              {/* Progress bar */}
              <div tw="bg-text_primary h-full rounded-full" style={{ width: `${progress}%` }} />

              {/* Thumb */}
              <div
                tw="absolute top-[50%] h-[12px] w-[12px] bg-text_primary rounded-full transform -translate-y-1/2 -translate-x-1/2 cursor-pointer"
                style={{ left: `${progress}%` }}
                onMouseDown={handleMouseDown} // Start dragging
              />
            </div>
          </div>
        </div>
        {/* duration & time elapsed */}
        {showElapsedTime && (
          <div
            css={tw`absolute top-[calc(100%+8px)] w-full flex justify-between text-[13px] font-medium text-text_tertiary`}
          >
            <p>{formatTime(elapsedTime)}</p>
            <p>{formatTime(duration)}</p>
          </div>
        )}
      </div>
      {/* Audio actions */}
      {!hideActions && (
        <div css={[tw`flex-shrink-0 w-[7%] flex gap-[0.375rem]`, actionContainerStyle]}>
          {/* Custom action buttons */}
          {customActionbtns &&
            customActionbtns.map((element, index) => React.cloneElement(element, { key: index }))}
          <img
            onClick={disableButtons ? null : handleDeleteAudio}
            src={DeleteIcon}
            alt={'delete audio'}
            css={[tw`w-full h-full cursor-pointer`, disableButtons && tw`cursor-not-allowed`]}
          />
        </div>
      )}
    </div>
  );
};

export default Audio;
