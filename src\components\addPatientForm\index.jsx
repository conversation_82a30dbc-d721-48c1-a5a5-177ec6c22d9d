import React from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import tw from 'twin.macro';
import GenericSelect from '@components/shared/select';
import { schemaAddPatient } from './schema';
import { InitialAddPatient } from './module';
import Input from '@components/shared/input';
import { useEffect } from 'react';
import UnitCompound from './unitCompound';
import ControlledCalendar from '../controlledCalendar';
import { gender_opetions, height_options, weight_options } from '@/constants/constants';
import PhoneNumber from '../phoneNumber';
import ActionFooter from '../actionFooter';
import ParenthesesText from '../parenthesesText';

const AddPatient = ({ customOnSubmit, customOnCancel }) => {
  const {
    control,
    register,
    handleSubmit,
    formState: { errors, isValid, touchedFields },
    trigger,
  } = useForm({
    defaultValues: {
      ...InitialAddPatient,
      heightUnit: height_options.find(opt => opt.value === InitialAddPatient.heightUnit.value),
      weightUnit: weight_options.find(opt => opt.value === InitialAddPatient.weightUnit.value),
    },
    resolver: yupResolver(schemaAddPatient),
    mode: 'onChange',
  });

  const weightUnit = useWatch({ control, name: 'weightUnit' });
  const heightUnit = useWatch({ control, name: 'heightUnit' });

  useEffect(() => {
    // Whenever weightUnit changes, re-validate the weight field
    trigger('weight');
  }, [weightUnit, trigger]);

  useEffect(() => {
    trigger('height');
  }, [heightUnit, trigger]);

  const onSubmit = data => {
    console.log({
      data,
    });
    customOnSubmit && customOnSubmit(data);
  };

  const nameFields = [
    {
      name: 'firstName',
      label: 'First name',
      placeholder: 'First Name',
      errorKey: 'firstName',
    },
    {
      name: 'lastName',
      label: 'Last name',
      placeholder: 'Last Name',
      errorKey: 'lastName',
    },
  ];

  const unitFields = [
    {
      labelText: 'Height',
      labelTooltipText: 'height toolTip',
      control,
      selectorName: 'heightUnit',
      selectorOptions: height_options,
      inputName: 'height',
      inputPlaceholder: '0 cm',
      register,
      errors,
      touchedFields,
    },
    {
      labelText: 'Weight',
      labelTooltipText: 'weight toolTip',
      control,
      selectorName: 'weightUnit',
      selectorOptions: weight_options,
      inputName: 'weight',
      inputPlaceholder: '0 kg',
      register,
      errors,
      touchedFields,
    },
  ];

  const baseButtonStyle = tw`flex-1 !py-[10px] !rounded-[6px] !font-medium`;
  const validButtonStyle = [baseButtonStyle, tw`text-text_primary bg-Primary !border-Primary_600`];
  const secondaryButtonStyle = [
    baseButtonStyle,
    tw`bg-white !border-border_stroke text-text_primary text-sm`,
  ];

  return (
    <form onSubmit={handleSubmit(onSubmit)} css={tw` flex flex-col`}>
      <div css={tw`p-8 pt-4 flex flex-col gap-6`}>
        {/*firstName & lastName*/}
        <div css={tw`flex gap-5`}>
          {nameFields.map(({ name, label, placeholder, errorKey }) => (
            <div css={tw`flex-1`} key={name}>
              <Input
                type="text"
                label={label}
                name={name}
                placeholder={placeholder}
                register={register}
                errorMessage={
                  errors[errorKey] && touchedFields[errorKey] ? errors[errorKey].message : ''
                }
                inputStye={tw`rounded-[6px] focus:outline-none focus:ring-2 focus:ring-Primary`}
              />
            </div>
          ))}
        </div>
        {/*phoneNumber & emailAddress*/}
        <div css={tw`w-full flex gap-5 items-start`}>
          <div css={tw`flex-1 flex flex-col gap-1`}>
            <PhoneNumber
              control={control}
              register={register}
              name="localNumber"
              placeholder="000-000-000"
              phoneName="countryCode"
              defaultCountry="EG"
              label="Phone number"
              errors={
                errors.localNumber && touchedFields.localNumber ? errors.localNumber.message : ''
              }
              inputType="text"
              hideErrorMessage
              inputCustomStyle={tw`h-[2.98rem]`}
            />
          </div>
          {/*emailAddress*/}
          <div css={tw`flex-1`}>
            <Input
              type="text"
              label={
                <label css={tw`flex items-center gap-1`}>
                  Email address
                  <ParenthesesText
                    text="optional"
                    textStyle={tw`text-text_tertiary font-light text-[13px]`}
                  />
                </label>
              }
              name="email"
              placeholder="<EMAIL>"
              register={register}
              errorMessage={errors.email?.message}
              inputStye={tw`h-[2.933rem] rounded-[6px] focus:outline-none focus:ring-2 focus:ring-Primary`}
            />
          </div>
        </div>
        {/*gender & weight & height */}
        <div css={tw`w-full flex gap-5`}>
          {/* gender */}
          <div css={tw`flex-1`}>
            <GenericSelect
              control={control}
              label="Gender"
              name="gender"
              placeholder="Select gender"
              options={gender_opetions}
              errorMessage={errors.gender && touchedFields.gender ? errors.gender.message : ''}
              labelStyle={tw`font-medium text-[0.95rem]`}
              labelContainerStyle={tw`mb-0`}
              customStyles={{
                control: () => ({
                  padding: '8px',
                }),
              }}
              inputLength={7}
            />
          </div>
          {/* height & weight */}
          {unitFields.map(field => (
            <UnitCompound key={field.inputName} {...field} />
          ))}
        </div>
        {/* dateOfBirth & condition*/}
        <div css={tw`flex gap-5`}>
          {/* dateOfBirth */}
          <div css={tw`flex-[1_1_0%]`}>
            <ControlledCalendar
              name="dateOfBirth"
              control={control}
              datePickerProps={{
                placeholder: 'mm/dd/yyyy',
                label: 'Date of Birth',
                labelClassName: '!font-medium !text-[0.95rem] !mb-1',
                dateFormat: {
                  day: 'numeric',
                  month: 'numeric',
                  year: 'numeric',
                },
                calendarClassName: 'bottom-[0%] left-[calc(100%+8px)] !w-[160%]',
                maxDate: new Date(),
              }}
              errors={errors}
            />
          </div>
          {/* condition */}
          <div css={tw`flex-[2_1_0%]`}>
            <Input
              type="text"
              label={
                <label css={tw`flex items-center gap-1`}>
                  Condition
                  <ParenthesesText
                    text="optional"
                    textStyle={tw`text-text_tertiary font-light text-[13px]`}
                  />
                </label>
              }
              name="condition"
              placeholder="A short description of the condition"
              register={register}
              errorMessage={errors.condition?.message}
              inputStye={tw`h-[3rem] rounded-[6px] focus:outline-none focus:ring-2 focus:ring-Primary`}
            />
          </div>
        </div>
      </div>
      {/*action buttons*/}
      <ActionFooter
        isPrimaryFirst
        containerStyle={tw`px-8 py-6 flex gap-3 bg-neutral_50 border-t border-stroke rounded-b-[12px]`}
        primaryButtonProps={{
          text: 'Save Patient',
          type: 'submit',
          disable: !isValid,
          customStyle: isValid ? validButtonStyle : baseButtonStyle,
        }}
        secondaryButtonProps={{
          text: 'Cancel',
          type: 'button',
          handelClick: customOnCancel,
          otherStyle: secondaryButtonStyle,
        }}
      />
    </form>
  );
};

export default AddPatient;
