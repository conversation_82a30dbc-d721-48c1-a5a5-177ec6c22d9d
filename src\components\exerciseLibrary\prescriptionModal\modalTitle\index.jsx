import React from 'react';
import VoiceMessageTitle from './voiceMessageTitle';
import ExerciseDetailsModelHeader from '@/components/exerciseLibrary/exerciseDetailsModelHeader';
import { ModalViews } from '..';
import tw from 'twin.macro';

const ModalTitle = ({ modalView, setModalView, handleClose }) => {
  const getTitleProps = modalView => {
    // Common properties
    const commonProps = {
      titleStyle: tw`text-[1.125rem]`,
      containerStyle: tw`!p-6 !border-none`,
    };

    // Dynamic properties based on modalView
    switch (modalView) {
      case ModalViews.ASSIGN_PRESCRIPTION:
        return {
          ...commonProps,
          title: 'Assign prescription',
          hideArrow: true,
          handelCloseClick: handleClose,
        };

      case ModalViews.ADD_PATIENT:
        return {
          ...commonProps,
          title: 'Add new patient',
          hideArrow: true,
          handelCloseClick: handleClose,
        };

      case ModalViews.PRE_SESSION_MESSAGES:
        return {
          ...commonProps,
          title: 'Pre-session messages',
          containerStyle: tw`!px-6 !py-[14px] !border-none`, // Override containerStyle
          handelCLiconTitle: () => setModalView(ModalViews.ASSIGN_PRESCRIPTION),
          hideCloseIcon: true,
        };

      default:
        return commonProps; // Fallback
    }
  };

  return (
    <>
      {modalView === ModalViews.VOICE_RECORDING || modalView === ModalViews.TEXT_TO_SPEECH ? (
        <VoiceMessageTitle modalView={modalView} setModalView={setModalView} />
      ) : (
        <ExerciseDetailsModelHeader {...getTitleProps(modalView)} />
      )}
    </>
  );
};

export default ModalTitle;
