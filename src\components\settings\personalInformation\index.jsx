import PhoneNumber from '@/components/phoneNumber';
import ProfilePicture from '@/components/profilePicture';
import LabelSections from '@/components/sectionLabels';
import Input from '@/components/shared/input';
import tw from 'twin.macro';

const PersonalInformation = ({ register, control }) => {
  const inputsData = [
    { id: 0, title: 'First name', name: 'firstName', register: register },
    { id: 1, title: 'Last name', name: 'lastName', register: register },
    { id: 2, title: 'Email', name: 'email', register: register },
  ];

  return (
    <div tw="space-y-4 p-[24px] border-b border-border_stroke">
      <LabelSections text={'Personal information'} />
      <div tw="flex gap-[35px]">
        <div tw="flex-shrink-0">
          <ProfilePicture text={'Upload a profile picture'} cancelIconStyle={tw`top-1 right-0`} />
        </div>
        <div tw="grid grid-cols-2 w-full gap-y-[24px] gap-x-[24px]">
          {inputsData.map(item => (
            <div tw="flex-1" key={item.id}>
              <Input
                containerSTyle={tw`flex-shrink-0`}
                label={item.title}
                register={register}
                name={item.name}
                placeholder={item.title}
                key={item.id}
              />
            </div>
          ))}
          <div tw="flex-1">
            <PhoneNumber
              control={control}
              defaultCountry={'EG'}
              label={'Phone number'}
              name={'phoneNumber'}
              phoneName={'phoneCh'}
              placeholder={'Phone number'}
              register={register}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PersonalInformation;
