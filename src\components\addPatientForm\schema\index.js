import * as yup from 'yup';

export const schemaAddPatient = yup
  .object({
    firstName: yup
      .string()
      .required('Required field.')
      .test(
        'is-valid-first-name',
        'Invalid name.',
        value => !value || /^[\u0600-\u06FFa-zA-Z]+(?: [\u0600-\u06FFa-zA-Z]+){0,2}$/.test(value)
      ),
    lastName: yup
      .string()
      .required('Required field.')
      .test(
        'is-valid-last-name',
        'Invalid name.',
        value => !value || /^[\u0600-\u06FFa-zA-Z]+(?: [\u0600-\u06FFa-zA-Z]+){0,1}$/.test(value)
      ),
    countryCode: yup.string().required('Required field.'),
    localNumber: yup
      .string()
      .required('Required field.')
      .matches(/^\d+$/, 'Invalid phone number.')
      .min(4, 'Invalid phone number.')
      .max(17, 'Invalid phone number.'),
    email: yup.string().email('Invalid email.').nullable().notRequired(),
    gender: yup
      .object()
      .shape({
        label: yup.string().required(),
        value: yup.string().required(),
      })
      .required('Required field.'),
    heightUnit: yup
      .object()
      .shape({
        label: yup.string().required(),
        value: yup.string().required(),
      })
      .required('Required field.'),
    height: yup
      .number()
      .typeError('Invalid height.')
      .required('Required field.')
      .positive('Invalid height.')
      .when('heightUnit', (heightUnit, schema) => {
        if (!heightUnit || !heightUnit[0].value) return schema;
        switch (heightUnit[0].value) {
          case 'FT':
            return schema.max(9, 'Invalid height.');
          case 'IN':
            return schema.max(100, 'Invalid height.');
          case 'M':
            return schema.max(2.5, 'Invalid height.');
          case 'CM':
            return schema.max(250, 'Invalid height.');
          default:
            return schema;
        }
      }),
    weightUnit: yup
      .object()
      .shape({
        label: yup.string().required(),
        value: yup.string().required(),
      })
      .required('Required field.'),
    weight: yup
      .number()
      .typeError('Invalid weight.')
      .required('Required field.')
      .positive('Invalid weight.')
      .when('weightUnit', (weightUnit, schema) => {
        if (!weightUnit || !weightUnit[0].value) return schema;
        switch (weightUnit[0].value) {
          case 'KG':
            return schema.max(500, 'Invalid weight.');
          case 'G':
            return schema.max(500000, 'Invalid weight.');
          case 'LB':
            return schema.max(1100, 'Invalid weight.');
          default:
            return schema;
        }
      }),
    dateOfBirth: yup.date().typeError('invalid date.').required('Required field.'),
    condition: yup.string().max(255, 'Maximum 255 characters.').nullable().notRequired(),
  })
  .required();
