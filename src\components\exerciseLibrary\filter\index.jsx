import { memo, useEffect, useState } from 'react';
import FilterCollapse from '../filterCollapse';
import 'twin.macro';

// Default filter items
const DEFAULT_FILTER_ITEMS = [
  { id: 'all', title: 'All' },
  { id: 0, title: 'sdd' },
  { id: 1, title: 'sdaddda' },
  {
    id: 2,
    title: 'custom',
    items: [],
  },
];

const DEFAULT_SECOND_ITEMS = [
  { id: 'all', title: 'All' },
  { id: 4, title: 'sdd2' },
  { id: 5, title: 'sdaddda335' },
  {
    id: 2,
    title: 'custom',
    items: [
      { id: 1, title: 'sdadddasss3423' },
      { id: 2, title: 'sdadddaaaa245' },
    ],
  },
];

// Main Filter Component
const Filter = ({
  register,
  watch,
  setValue,
  control,
  collapseItems = [],
  state,
  dispatch,
  setSearchParams,
}) => {
  // Use provided collapseItems or default items
  const items =
    collapseItems.length > 0
      ? collapseItems
      : [
          { title: 'First filter', data: DEFAULT_FILTER_ITEMS },
          { title: 'Second filter', data: DEFAULT_SECOND_ITEMS },
        ];

  // Merge custom filters from state into the filter items
  const updatedItems = items.map(filterGroup => {
    const updatedData = filterGroup.data.map(item => {
      if (item.title === 'custom' && state.customFilters && state.customFilters.length > 0) {
        return {
          ...item,
          items: [...item.items, ...state.customFilters],
        };
      }
      return item;
    });
    return {
      ...filterGroup,
      data: updatedData,
    };
  });

  const [openCollapse, setOpenCollapse] = useState([0]); // Initialize with first item open
  const watchedFields = watch();

  useEffect(() => {
    // const filters = generateFilterPayload(watchedFields);
    // console.log('Sending to backend:', filters);
  }, [watchedFields]);

  return (
    <div
      className="element"
      tw="border overflow-y-auto w-[16%] sticky top-0 left-0 h-[105 %] py-[20px] border-border_stroke rounded-card"
    >
      {updatedItems.map((filterItems, index) => (
        <FilterCollapse
          key={index}
          title={filterItems.title}
          index={index}
          filterItems={filterItems.data}
          openCollapse={openCollapse}
          setOpenCollapse={setOpenCollapse}
          register={register}
          control={control}
          watch={watch}
          dispatch={dispatch}
          state={state}
          setValue={setValue}
          setSearchParams={setSearchParams}
          isLastItem={index === items.length - 1}
        />
      ))}
    </div>
  );
};

export default memo(Filter);
