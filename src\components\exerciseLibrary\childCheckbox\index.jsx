import Checkbox from '@/components/shared/checkbox';
import { truncateText } from '@/utils/helpers';
import { Controller } from 'react-hook-form';
import deleteIcon from '@assets/svgs/delete-red.svg';
import editIcon from '@assets/svgs/exercise-library/edit-icon.svg';
import tw from 'twin.macro';
import React, { useRef, memo } from 'react';
import Input from '@/components/shared/input';
import { exerciseLibraryActions } from '@/reducers/exercise-library';

// Child Checkbox Component
const ChildCheckbox = ({
  item,
  control,
  watch,
  setValue,
  filterItems,
  onEdit,
  onDelete,
  register,
  dispatch,
  state,
}) => {
  // const [isEditing, setIsEditing] = useState(false);
  const inputRef = useRef(null);

  const handleEditClick = e => {
    e.stopPropagation();
    dispatch({
      type: exerciseLibraryActions.openCollapseModel,
      payload: false,
    });
    // setValue('filter_custom_name', e.target.value);
    dispatch({ type: exerciseLibraryActions.isEditFilter, payload: true });
    dispatch({ type: exerciseLibraryActions.setEditFilterId, payload: item.id });
  };

  const handleDeleteClick = e => {
    e.stopPropagation();
    dispatch({ type: exerciseLibraryActions.deleteCustomFilter, payload: item.id });
  };

  const handleInputBlur = () => {
    const newValue = inputRef.current ? inputRef.current.value : item.title;
    setValue('filter_custom_name', newValue.trim());
    if (onEdit && newValue.trim() && newValue !== item.title) {
      onEdit(item.id, newValue.trim());
    }
  };

  return state.isEditFilter && state.editFilterId === item.id ? (
    <div tw="py-[4px]">
      <Input
        name={'filter_custom_name'}
        defaultValue={item.title}
        onBlur={handleInputBlur}
        inputStye={tw`px-2 py-1 text-sm`}
        autoFocus
        ref={inputRef}
        register={register}
      />
    </div>
  ) : (
    <Controller
      name={`marketingConsent_custom_${item.id}`}
      control={control}
      render={({ field }) => (
        <Checkbox
          containerStyle={tw`items-center!`}
          label={truncateText({ text: item.title, maxLength: 5 })}
          checked={field.value}
          hasHovering
          otherSection={
            <div tw="flex gap-1 items-center">
              <img
                src={editIcon}
                alt="edit"
                tw="w-[13px] cursor-pointer"
                onClick={handleEditClick}
              />
              <img
                src={deleteIcon}
                alt="delete"
                tw="w-[13px] cursor-pointer"
                onClick={handleDeleteClick}
              />
            </div>
          }
          onChange={e => {
            const checked = e.target.checked;
            field.onChange(checked);

            const isAllChecked = watch('all');
            if (isAllChecked && checked) {
              setValue('all', false);

              filterItems.forEach(i => {
                if (i.id !== 'all') {
                  setValue(`marketingConsent_${i.id}`, true);
                  if (i.items) {
                    i.items.forEach(_val => {
                      const childField = `marketingConsent_custom_${_val.id}`;
                      setValue(childField, _val.id !== item.id);
                    });
                  }
                }
              });

              setValue(field.name, false);
            }
          }}
        />
      )}
    />
  );
};

export default memo(ChildCheckbox);
