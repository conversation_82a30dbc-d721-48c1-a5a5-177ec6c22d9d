import { styles } from './datePicker';
import tw from 'twin.macro';

// Navigation Label Component
const NavigationLabel = ({
  years,
  date,
  locale,
  view,
  setView,
  navigateToYear,
  toggleYearsList,
  primaryColor = styles.primaryColor,
  textDark = styles.textDark,
}) => {
  const currentYear = date.getFullYear();
  const prevYear = currentYear - 1;
  const nextYear = currentYear + 1;

  // Helper to check if a year is enabled
  const isYearEnabled = year => years.includes(year);

  if (view === 'month') {
    return (
      <div tw="text-[18px] flex justify-center items-center">
        <div
          tw="w-fit text-center text-[14px] text-white rounded-[8px] px-[14px] font-[700] py-[4px] cursor-pointer"
          css={[{ backgroundColor: primaryColor }]}
          onClick={() => setView('year')}
        >
          {date.toLocaleDateString(locale, {
            month: 'long',
            year: 'numeric',
          })}
        </div>
      </div>
    );
  }

  return (
    <div
      css={[tw`grid grid-cols-3 justify-items-center items-center bg-white cursor-default`]}
      onClick={e => {
        e.stopPropagation();
      }}
    >
      <div
        id="prev-year"
        tw="w-fit text-center text-[14px] rounded-[5px] px-[12px] font-[700] cursor-pointer"
        css={[
          { color: textDark },
          isYearEnabled(prevYear)
            ? tw`opacity-100 cursor-pointer`
            : tw`opacity-50 cursor-not-allowed`,
        ]}
        onClick={e => {
          if (isYearEnabled(prevYear)) {
            navigateToYear(prevYear);
          }
          e.stopPropagation();
        }}
      >
        {prevYear}
      </div>

      <div
        id="current-year"
        css={[
          tw`w-fit text-center text-[14px] text-white rounded-[8px] px-[12px] font-[700] py-[6px]`,
          years.length === 1 ? tw`cursor-default` : tw`cursor-pointer`,
          { backgroundColor: primaryColor },
        ]}
        onClick={e => {
          e.stopPropagation();
          if (years.length > 1) {
            toggleYearsList();
          }
        }}
      >
        {currentYear}
      </div>
      <div
        id="next-year"
        tw="w-fit text-center text-[14px] rounded-[8px] px-[14px] font-[700] py-[6px]"
        css={[
          { color: textDark },
          isYearEnabled(nextYear)
            ? tw`opacity-100 cursor-pointer`
            : tw`opacity-50 cursor-not-allowed`,
        ]}
        onClick={e => {
          if (isYearEnabled(nextYear)) {
            navigateToYear(nextYear);
          }
          e.stopPropagation();
        }}
      >
        {nextYear}
      </div>
    </div>
  );
};

export default NavigationLabel;
