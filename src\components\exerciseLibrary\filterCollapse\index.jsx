import Collapse from '@/components/shared/collapse';
import SearchInput from '../searchInput';
import FilterItemsContainer from '../filterContainer';
import tw from 'twin.macro';

// Single Collapse Component
const FilterCollapse = ({
  index,
  title,
  filterItems,
  openCollapse,
  setOpenCollapse,
  register,
  control,
  watch,
  setValue,
  isLastItem,
  state,
  dispatch,
  setSearchParams,
}) => {
  return (
    <div>
      <Collapse
        key={index}
        open={openCollapse.includes(index)}
        handelToggleCollapse={() => {
          if (openCollapse.includes(index)) {
            // Remove from open list
            setOpenCollapse(openCollapse.filter(item => item !== index));
          } else {
            // Add to open list
            setOpenCollapse([...openCollapse, index]);
          }
        }}
        header={title}
        body={
          <div>
            <SearchInput register={register} />
            <FilterItemsContainer
              filterItems={filterItems}
              control={control}
              watch={watch}
              setValue={setValue}
              dispatch={dispatch}
              state={state}
              register={register}
              setSearchParams={setSearchParams}
            />
          </div>
        }
        arrowIconCustomStyle={tw`w-[10px]!`}
        headerCustomStyle={tw`text-[0.75rem] font-semibold text-[#000] px-[20px]`}
        bodyCustomStyle={tw`px-[20px]`}
      />
      {!isLastItem && <div tw="w-full h-[1px] my-[8px] bg-[#D9D9D9]" />}
    </div>
  );
};
export default FilterCollapse;
