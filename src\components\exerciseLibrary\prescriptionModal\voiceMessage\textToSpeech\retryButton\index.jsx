import React, { useState } from 'react';
import ResetIcon from '@assets/svgs/audio/reset.svg';
import tw from 'twin.macro';
import ProgressCircle from '@/components/shared/progressCircle';

const FILE_URL =
  'https://p.scdn.co/mp3-preview/2726a9595503bf33fdf44d0e85ae8abc7d876d44?cid=774b29d4f13844c495f206cafdad9c86';

const RetryButton = ({ retrying, setRetrying }) => {
  const [progress, setProgress] = useState(0);

  const handleRetry = async () => {
    setRetrying(true); // Show the loader by setting retrying to true
    setProgress(0); // Reset progress to 0 at the start

    try {
      const response = await fetch(FILE_URL); // Fetch the audio file from the URL
      const contentLength = response.headers.get('Content-Length'); // Get the total file size from headers

      const total = parseInt(contentLength, 10); // Parse the total size as an integer
      let loaded = 0; // Initialize loaded bytes counter
      const reader = response.body.getReader(); // Get a stream reader for the response body

      while (true) {
        const { done, value } = await reader.read(); // Read the next chunk from the stream
        if (done) break; // Exit loop if all chunks are read
        loaded += value.length; // Add chunk size to loaded bytes
        setProgress(loaded / total); // Update progress (as a fraction between 0 and 1)
      }
      setProgress(1); // Ensure progress is set to 100% when done
    } catch (err) {
      console.error(err); // Log any errors that occur during fetch
    } finally {
      setTimeout(() => setRetrying(false), 500); // Hide loader after a short delay for UX
    }
  };

  return (
    <>
      {!retrying ? (
        <img
          src={ResetIcon}
          alt="Retry"
          css={tw`w-full h-full cursor-pointer`}
          onClick={handleRetry}
        />
      ) : (
        <div css={tw`w-full h-full flex items-center justify-center`}>
          <ProgressCircle progress={progress} />
        </div>
      )}
    </>
  );
};

export default RetryButton;
